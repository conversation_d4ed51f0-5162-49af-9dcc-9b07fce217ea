import json
import os
import urllib.request
import urllib.parse
import boto3

def get_secret_from_ssm(param_name):
    ssm_client = boto3.client('ssm')
    try:
        response = ssm_client.get_parameter(
            Name=param_name,
            WithDecryption=True
        )
        return response['Parameter']['Value']
    except Exception as e:
        print(f"Error retrieving SSM parameter {param_name}: {e}")
        return None

def lambda_handler(event, context):
    print(f"Received event: {json.dumps(event)}")

    # Extract the city from the Lex event
    city = None
    if 'sessionState' in event and 'intent' in event['sessionState'] and 'slots' in event['sessionState']['intent']:
        if 'City' in event['sessionState']['intent']['slots'] and \
           event['sessionState']['intent']['slots']['City'] and \
           'value' in event['sessionState']['intent']['slots']['City']:
            city = event['sessionState']['intent']['slots']['City']['value']['resolvedValues'][0]
            print(f"Extracted city: {city}")
        else:
            print("City slot not found or empty in event.")
    else:
        print("Session state or intent not found in event.")

    if not city:
        return {
            "sessionState": {
                "dialogAction": {
                    "type": "ElicitSlot",
                    "slotToElicit": "City"
                },
                "intent": {
                    "name": "GetWeatherForecast",
                    "slots": event['sessionState']['intent']['slots'],
                    "state": "InProgress"
                }
            }
        }

    # Retrieve OpenWeatherMap API key from SSM Parameter Store
    openweathermap_api_key_ssm_path = os.environ.get('OPENWEATHERMAP_API_KEY_SSM_PATH')
    if not openweathermap_api_key_ssm_path:
        print("OPENWEATHERMAP_API_KEY_SSM_PATH environment variable not set.")
        return {
            "sessionState": {
                "dialogAction": {
                    "type": "Close"
                },
                "intent": {
                    "name": "GetWeatherForecast",
                    "state": "Failed"
                }
            },
            "messages": [
                {
                    "contentType": "PlainText",
                    "content": "I'm sorry, I cannot retrieve the weather forecast at this time due to a configuration error."
                }
            ]
        }

    api_key = get_secret_from_ssm(openweathermap_api_key_ssm_path)
    if not api_key:
        return {
            "sessionState": {
                "dialogAction": {
                    "type": "Close"
                },
                "intent": {
                    "name": "GetWeatherForecast",
                    "state": "Failed"
                }
            },
            "messages": [
                {
                    "contentType": "PlainText",
                    "content": "I'm sorry, I cannot retrieve the weather forecast at this time. The API key is missing."
                }
            ]
        }

    # Call OpenWeatherMap API
    base_url = "http://api.openweathermap.org/data/2.5/weather?"
    params = {
        "q": city,
        "appid": api_key,
        "units": "metric" # or 'imperial' for Fahrenheit
    }
    url = base_url + urllib.parse.urlencode(params)

    try:
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
            print(f"OpenWeatherMap API response: {json.dumps(data)}")

            if data.get("cod") == 200:
                weather_condition = data["weather"][0]["description"]
                temperature = data["main"]["temp"]
                response_message = f"The weather in {city} is {weather_condition} with a temperature of {temperature} degrees Celsius."
            else:
                error_message = data.get("message", "Unknown error")
                response_message = f"I couldn't get the weather for {city}. Error: {error_message}"

    except urllib.error.URLError as e:
        print(f"Error calling OpenWeatherMap API: {e}")
        response_message = f"I'm sorry, I couldn't connect to the weather service. Please try again later."
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        response_message = f"An unexpected error occurred while fetching weather for {city}."

    # Return response to Lex
    return {
        "sessionState": {
            "dialogAction": {
                "type": "Close"
            },
            "intent": {
                "name": "GetWeatherForecast",
                "slots": {
                    "City": {
                        "value": {
                            "originalValue": city,
                            "resolvedValues": [city],
                            "interpretedValue": city
                        }
                    },
                    "WeatherCondition": {
                        "value": {
                            "originalValue": weather_condition if 'weather_condition' in locals() else "N/A",
                            "resolvedValues": [weather_condition] if 'weather_condition' in locals() else ["N/A"],
                            "interpretedValue": weather_condition if 'weather_condition' in locals() else "N/A"
                        }
                    },
                    "Temperature": {
                        "value": {
                            "originalValue": str(temperature) if 'temperature' in locals() else "N/A",
                            "resolvedValues": [str(temperature)] if 'temperature' in locals() else ["N/A"],
                            "interpretedValue": str(temperature) if 'temperature' in locals() else "N/A"
                        }
                    }
                },
                "state": "Fulfilled"
            }
        },
        "messages": [
            {
                "contentType": "PlainText",
                "content": response_message
            }
        ]
    }
